{"name": "filling-service", "version": "1.0.0", "description": "Filling Records Microservice for Coconut Backend Services", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["filling", "records", "microservice", "coconut", "backend"], "author": "Coconut Backend Services", "license": "ISC", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-rate-limit": "^6.10.0", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8"}, "devDependencies": {"@types/express": "^4.17.17", "@types/node": "^20.5.0", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.2", "@types/bcryptjs": "^2.4.2", "@types/swagger-ui-express": "^4.1.3", "@types/swagger-jsdoc": "^6.0.1", "typescript": "^5.1.6", "ts-node-dev": "^2.0.0"}}