import Joi from "joi";

// Schema for business names
const businessNamesSchema = Joi.object({
  type: Joi.string().required().trim(),
  name1: Joi.string().required().trim(),
  name2: Joi.string().optional().trim(),
  name3: Joi.string().optional().trim(),
  phone: Joi.string().required().trim(),
  email: Joi.string().email().required().trim().lowercase(),
  businessNature: Joi.string().required().trim(),
});

// Schema for director
const directorSchema = Joi.object({
  firstName: Joi.string().required().trim(),
  lastName: Joi.string().required().trim(),
  otherName: Joi.string().optional().trim(),
  dateOfBirth: Joi.string().isoDate().required(),
  gender: Joi.string().valid("male", "female", "other").required(),
  nationality: Joi.string().required().trim(),
  phone: Joi.string().required().trim(),
  email: Joi.string().email().required().trim().lowercase(),
  occupation: Joi.string().required().trim(),
  address: Joi.string().required().trim(),
  nin: Joi.string().required().trim(),
  identification: Joi.string().required().trim(),
  passport: Joi.string().required().trim(),
  signature: Joi.string().required().trim(),
});

// Schema for witness
const witnessSchema = Joi.object({
  firstName: Joi.string().required().trim(),
  lastName: Joi.string().required().trim(),
  otherName: Joi.string().optional().trim(),
  dateOfBirth: Joi.string().isoDate().required(),
  gender: Joi.string().valid("male", "female", "other").required(),
  nationality: Joi.string().required().trim(),
  phone: Joi.string().required().trim(),
  email: Joi.string().email().required().trim().lowercase(),
  occupation: Joi.string().required().trim(),
  address: Joi.string().required().trim(),
  witnessSignature: Joi.string().optional().trim(),
});

// Main registration form schema
export const RegistrationFormSchema = Joi.object({
  businessId: Joi.string().required().trim(),
  details: Joi.object({
    businessNames: businessNamesSchema.required(),
    director: directorSchema.required(),
    witness: witnessSchema.required(),
  }).required(),
  txId: Joi.string().required().trim(),
  status: Joi.string().valid("pending", "approved", "rejected").optional(),
  document: Joi.string().required().trim(),
});

// Schema for updating registration form
export const UpdateRegistrationDtoSchema = Joi.object({
  details: Joi.object({
    businessNames: businessNamesSchema.optional(),
    director: directorSchema.optional(),
    witness: witnessSchema.optional(),
  }).optional(),
  status: Joi.string().valid("pending", "approved", "rejected").optional(),
  document: Joi.string().optional().trim(),
});

// Schema for adding file details
export const AddFileDetails = Joi.object({
  files: Joi.array().items(
    Joi.object({
      title: Joi.string().required().trim(),
      url: Joi.string().uri().required(),
    })
  ).optional(),
  title: Joi.string().optional().trim(),
  url: Joi.string().uri().optional(),
}).or('files', 'title');
