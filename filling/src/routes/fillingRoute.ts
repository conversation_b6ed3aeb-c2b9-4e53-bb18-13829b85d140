import { Router } from "express";
import FillingRecordController from "../controllers/fillingRecords";
import { AddFileDetails, RegistrationFormSchema, UpdateRegistrationDtoSchema } from "../schemas/fillingRequestSchema";
import { validate } from "../middlewares/validate";
import { adminProtect, protect } from "../middlewares/auth";

const router = Router()

router.post(
  "/create",
  validate({ body: RegistrationFormSchema }),
  protect,
  FillingRecordController.create   
);

router.post("/add-file/:id", adminProtect, validate({body: AddFileDetails}), FillingRecordController.addFileDetails); 

router.get("/business/:businessId", protect, FillingRecordController.fetchByBusinessId);

router.get("/all", adminProtect, FillingRecordController.fetchAll);

router.put("/:id", adminProtect, validate({body: UpdateRegistrationDtoSchema}), FillingRecordController.UpdateRecord);

export default router;
