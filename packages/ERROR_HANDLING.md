# Error Handling Implementation

This document describes the comprehensive error handling system implemented in the Packages microservice.

## Overview

The error handling system uses a centralized approach with the `handleError` method pattern you requested, providing consistent error handling across all layers of the application.

## Key Components

### 1. ErrorHandler Utility (`src/utils/errorHandler.ts`)

The main error handling utility that implements the `handleError` method pattern:

```typescript
private static handleError(error: any, action: string): Error {
  if (axios.isAxiosError(error)) {
    return new Error(
      `Error ${action}: ${error.response?.status} - ${
        error.response?.data?.message || error.message
      }`
    );
  }
  return new Error(`Unexpected error ${action}: ${error.message}`);
}
```

**Features:**
- Handles Axios errors with proper status codes and messages
- Handles MongoDB errors (ValidationError, CastError, MongoServerError)
- Handles JWT errors (JsonWebTokenError, TokenExpiredError)
- Handles network and database connection errors
- Provides detailed error logging
- Creates standardized error objects

### 2. Enhanced ResponseUtil (`src/utils/response.ts`)

Updated response utility with integrated error handling:

```typescript
static handleErrorResponse(res: Response, error: any, action: string, statusCode?: number): void {
  const errorDetails = ErrorHandler.handleError(error, action);
  ErrorHandler.logError(errorDetails);
  
  const responseStatusCode = statusCode || errorDetails.statusCode;
  this.error(res, errorDetails.message, errorDetails.originalError?.stack, responseStatusCode);
}
```

### 3. Error Types

The system recognizes and handles these error types:

- **ValidationError**: MongoDB validation errors (400)
- **CastError**: Invalid data format errors (400)
- **MongoServerError**: Database constraint errors (409)
- **JsonWebTokenError**: Invalid JWT tokens (401)
- **TokenExpiredError**: Expired JWT tokens (401)
- **AxiosError**: HTTP request errors (varies)
- **NetworkError**: Connection issues (503)
- **DatabaseError**: Database connection problems (503)

## Implementation Across Layers

### Service Layer (`src/services/PackageService.ts`)

All service methods use the error handling pattern:

```typescript
async createPackage(packageData: PackageInterface): Promise<IPackage> {
  try {
    const newPackage = new Package(packageData);
    const savedPackage = await newPackage.save();
    logger.info(`Package created successfully with ID: ${savedPackage._id}`);
    return savedPackage;
  } catch (error) {
    logger.error('Error creating package:', error);
    ResponseUtil.handleServiceError(error, 'creating package');
  }
}
```

### Controller Layer (`src/controllers/PackageController.ts`)

Controllers use the `asyncHandler` middleware which automatically catches errors and passes them to the error handler.

### Middleware Layer

#### Error Handler Middleware (`src/middleware/errorHandler.ts`)

```typescript
export const errorHandler = (
  error: CustomError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  logger.error(`Error handling request: ${error.message}`, {
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  ResponseUtil.handleErrorResponse(res, error, 'handling request', error.statusCode);
};
```

#### Validation Middleware (`src/middleware/validation.ts`)

```typescript
} catch (error) {
  ResponseUtil.handleErrorResponse(res, error, 'validating package data', 400);
}
```

### Database Layer (`src/config/database.ts`)

Database connection errors are handled consistently:

```typescript
} catch (error) {
  logger.error('Failed to connect to MongoDB:', error);
  ResponseUtil.handleServiceError(error, 'connecting to MongoDB');
}
```

## Error Response Format

All errors return a consistent JSON structure:

```json
{
  "success": false,
  "message": "Error description with context",
  "error": "Stack trace (development only)",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Testing Error Handling

Run the error handling test script:

```bash
node test-error-handling.js
```

This script tests various error scenarios:

1. **Authentication Errors**: Invalid admin tokens
2. **Validation Errors**: Invalid package data
3. **Format Errors**: Invalid MongoDB ObjectId format
4. **Not Found Errors**: Non-existent packages
5. **Search Validation**: Empty search queries
6. **Parameter Validation**: Invalid price ranges

## Error Logging

The system provides comprehensive error logging with different levels:

- **Error Level**: Server errors (5xx status codes)
- **Warning Level**: Client errors (4xx status codes)
- **Info Level**: General error information

Log entries include:
- Error type and message
- HTTP status code
- Request context (URL, method, IP, user agent)
- Stack trace
- Timestamp

## Benefits

1. **Consistency**: All errors are handled using the same pattern
2. **Maintainability**: Centralized error handling logic
3. **Debugging**: Detailed error logging and context
4. **Security**: Sensitive error details hidden in production
5. **User Experience**: Clear, actionable error messages
6. **Monitoring**: Structured error data for monitoring systems

## Usage Examples

### In Service Methods

```typescript
try {
  // Your business logic here
  return result;
} catch (error) {
  logger.error('Error in operation:', error);
  ResponseUtil.handleServiceError(error, 'performing operation');
}
```

### In Controllers (with asyncHandler)

```typescript
someMethod = asyncHandler(async (req: Request, res: Response) => {
  // Your controller logic here
  // Errors are automatically caught and handled
});
```

### In Middleware

```typescript
} catch (error) {
  ResponseUtil.handleErrorResponse(res, error, 'middleware operation', 400);
}
```

This error handling system ensures that all errors in the Packages microservice are handled consistently, logged appropriately, and returned to clients in a standardized format.
