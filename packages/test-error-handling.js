const axios = require('axios');

// Test data for creating a package
const validPackageData = {
  "title": "Test Premium Gift Box",
  "shortDescription": "Test elegant gift packaging for special occasions",
  "description": "Test premium gift box collection features high-quality materials, sophisticated design, and customizable options. Perfect for testing error handling.",
  "variations": [
    {
      "title": "Box Color",
      "optionType": "dropdown",
      "options": ["Red", "Blue", "Gold", "Silver"]
    },
    {
      "title": "Ribbon Style",
      "optionType": "images",
      "options": ["Satin Ribbon", "Velvet Ribbon"]
    }
  ],
  "quantity": [
    {
      "size": "Small (15x15x8 cm)",
      "price": 24.99
    },
    {
      "size": "Medium (25x25x12 cm)",
      "price": 39.99
    }
  ]
};

// Invalid package data for testing validation errors
const invalidPackageData = {
  "title": "", // Empty title should trigger validation error
  "shortDescription": "Test description",
  "description": "Test description",
  "variations": [
    {
      "title": "Color",
      "optionType": "invalid_type", // Invalid option type
      "options": []
    }
  ],
  "quantity": [
    {
      "size": "Small",
      "price": -10 // Negative price should trigger validation error
    }
  ]
};

const BASE_URL = 'http://localhost:8001/api';

async function testErrorHandling() {
  console.log('🧪 Testing Error Handling for Packages API\n');

  // Test 1: Valid package creation (should succeed)
  console.log('1. Testing valid package creation...');
  try {
    const response = await axios.post(`${BASE_URL}/packages`, validPackageData, {
      headers: {
        'Content-Type': 'application/json',
        // Note: This will fail without proper admin token, which is expected
        'Authorization': 'Bearer fake_admin_token'
      }
    });
    console.log('✅ Package created successfully:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('❌ Expected error (no valid admin token):', {
        status: error.response.status,
        message: error.response.data.message,
        timestamp: error.response.data.timestamp
      });
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n2. Testing invalid package data validation...');
  try {
    const response = await axios.post(`${BASE_URL}/packages`, invalidPackageData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer fake_admin_token'
      }
    });
    console.log('✅ Unexpected success:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('✅ Expected validation error:', {
        status: error.response.status,
        message: error.response.data.message,
        success: error.response.data.success,
        timestamp: error.response.data.timestamp
      });
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n3. Testing invalid package ID format...');
  try {
    const response = await axios.get(`${BASE_URL}/packages/invalid_id_format`);
    console.log('✅ Unexpected success:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('✅ Expected ID format error:', {
        status: error.response.status,
        message: error.response.data.message,
        success: error.response.data.success,
        timestamp: error.response.data.timestamp
      });
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n4. Testing non-existent package...');
  try {
    const response = await axios.get(`${BASE_URL}/packages/507f1f77bcf86cd799439011`);
    console.log('✅ Unexpected success:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('✅ Expected not found error:', {
        status: error.response.status,
        message: error.response.data.message,
        success: error.response.data.success,
        timestamp: error.response.data.timestamp
      });
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n5. Testing search with empty query...');
  try {
    const response = await axios.get(`${BASE_URL}/packages/search?q=`);
    console.log('✅ Unexpected success:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('✅ Expected search validation error:', {
        status: error.response.status,
        message: error.response.data.message,
        success: error.response.data.success,
        timestamp: error.response.data.timestamp
      });
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n6. Testing invalid price range...');
  try {
    const response = await axios.get(`${BASE_URL}/packages/price-range?minPrice=100&maxPrice=50`);
    console.log('✅ Unexpected success:', response.data);
  } catch (error) {
    if (error.response) {
      console.log('✅ Expected price range validation error:', {
        status: error.response.status,
        message: error.response.data.message,
        success: error.response.data.success,
        timestamp: error.response.data.timestamp
      });
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n7. Testing server health check (should succeed)...');
  try {
    const response = await axios.get(`${BASE_URL.replace('/api', '')}/health`);
    console.log('✅ Health check successful:', {
      status: response.data.status,
      service: response.data.service,
      database: response.data.database
    });
  } catch (error) {
    if (error.response) {
      console.log('❌ Health check failed:', error.response.data);
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n8. Testing API documentation (should succeed)...');
  try {
    const response = await axios.get(`${BASE_URL}/`);
    console.log('✅ API documentation accessible:', {
      service: response.data.service,
      version: response.data.version
    });
  } catch (error) {
    if (error.response) {
      console.log('❌ API documentation failed:', error.response.data);
    } else {
      console.log('❌ Network error:', error.message);
    }
  }

  console.log('\n🎉 Error handling tests completed!');
}

// Run the tests
testErrorHandling().catch(console.error);
