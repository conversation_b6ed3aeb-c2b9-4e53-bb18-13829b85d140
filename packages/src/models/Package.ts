import mongoose, { Schema, Document } from 'mongoose';
import { PackageInterface, Variations, Quantity } from '../types/package.interface';

export interface IPackage extends PackageInterface, Document {
  _id: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const VariationsSchema = new Schema<Variations>({
  title: {
    type: String,
    required: [true, 'Variation title is required'],
    trim: true,
  },
  optionType: {
    type: String,
    required: [true, 'Option type is required'],
    enum: {
      values: ['images', 'default', 'dropdown'],
      message: 'Option type must be either images, default, or dropdown',
    },
  },
  options: {
    type: [String],
    required: [true, 'Options are required'],
    validate: {
      validator: function(options: string[]) {
        return options && options.length > 0 && options.every(option => option.trim().length > 0);
      },
      message: 'Options must be a non-empty array of non-empty strings',
    },
  },
}, { _id: false });

const QuantitySchema = new Schema<Quantity>({
  size: {
    type: String,
    required: [true, 'Size is required'],
    trim: true,
  },
  price: {
    type: Number,
    required: [true, 'Price is required'],
    min: [0, 'Price must be a positive number'],
  },
}, { _id: false });

const PackageSchema = new Schema<IPackage>({
  title: {
    type: String,
    required: [true, 'Package title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters'],
  },
  shortDescription: {
    type: String,
    required: [true, 'Short description is required'],
    trim: true,
    maxlength: [500, 'Short description cannot exceed 500 characters'],
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [2000, 'Description cannot exceed 2000 characters'],
  },
  variations: {
    type: [VariationsSchema],
    required: [true, 'Variations are required'],
    validate: {
      validator: function(variations: Variations[]) {
        return variations && variations.length > 0;
      },
      message: 'At least one variation is required',
    },
  },
  quantity: {
    type: [QuantitySchema],
    required: [true, 'Quantity options are required'],
    validate: {
      validator: function(quantities: Quantity[]) {
        return quantities && quantities.length > 0;
      },
      message: 'At least one quantity option is required',
    },
  },
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
  toObject: {
    transform: function(doc, ret) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
});

// Indexes for better quey for better performance
PackageSchema.index({ title: 1 });
PackageSchema.index({ createdAt: -1 });
PackageSchema.index({ 'variations.title': 1 });

// Pre-save middleware to ensure data consistency
PackageSchema.pre('save', function(next) {
  // Trim all string fields
  if (this.title) this.title = this.title.trim();
  if (this.shortDescription) this.shortDescription = this.shortDescription.trim();
  if (this.description) this.description = this.description.trim();
  
  // Trim variation titles and options
  if (this.variations) {
    this.variations.forEach(variation => {
      if (variation.title) variation.title = variation.title.trim();
      if (variation.options) {
        variation.options = variation.options.map(option => option.trim()).filter(option => option.length > 0);
      }
    });
  }
  
  // Trim quantity sizes
  if (this.quantity) {
    this.quantity.forEach(qty => {
      if (qty.size) qty.size = qty.size.trim();
    });
  }
  
  next();
});

export const Package = mongoose.model<IPackage>('Package', PackageSchema);
