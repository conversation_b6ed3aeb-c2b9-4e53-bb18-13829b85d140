import { Request, Response, NextFunction } from 'express';
import { ResponseUtil } from '../utils/response';
import { PackageInterface, Variations, Quantity } from '../types/package.interface';

export const validatePackageData = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const { title, shortDescription, description, variations, quantity } = req.body;

    // Validate required fields
    if (!title || typeof title !== 'string' || title.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Title is required and must be a non-empty string');
      return;
    }

    if (!shortDescription || typeof shortDescription !== 'string' || shortDescription.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Short description is required and must be a non-empty string');
      return;
    }

    if (!description || typeof description !== 'string' || description.trim().length === 0) {
      ResponseUtil.badRequest(res, 'Description is required and must be a non-empty string');
      return;
    }

    // Validate variations
    if (!Array.isArray(variations)) {
      ResponseUtil.badRequest(res, 'Variations must be an array');
      return;
    }

    for (const variation of variations) {
      if (!validateVariation(variation)) {
        ResponseUtil.badRequest(res, 'Invalid variation format');
        return;
      }
    }

    // Validate quantity
    if (!Array.isArray(quantity)) {
      ResponseUtil.badRequest(res, 'Quantity must be an array');
      return;
    }

    for (const qty of quantity) {
      if (!validateQuantity(qty)) {
        ResponseUtil.badRequest(res, 'Invalid quantity format');
        return;
      }
    }

    next();
  } catch (error) {
    ResponseUtil.badRequest(res, 'Validation error', error instanceof Error ? error.message : 'Unknown error');
  }
};

const validateVariation = (variation: any): variation is Variations => {
  if (!variation || typeof variation !== 'object') {
    return false;
  }

  const { title, optionType, options } = variation;

  if (!title || typeof title !== 'string' || title.trim().length === 0) {
    return false;
  }

  if (!optionType || !['images', 'default', 'dropdown'].includes(optionType)) {
    return false;
  }

  if (!Array.isArray(options) || options.length === 0) {
    return false;
  }

  return options.every(option => typeof option === 'string' && option.trim().length > 0);
};

const validateQuantity = (quantity: any): quantity is Quantity => {
  if (!quantity || typeof quantity !== 'object') {
    return false;
  }

  const { size, price } = quantity;

  if (!size || typeof size !== 'string' || size.trim().length === 0) {
    return false;
  }

  if (typeof price !== 'number' || price < 0) {
    return false;
  }

  return true;
};

export const validatePackageUpdate = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    const { title, shortDescription, description, variations, quantity } = req.body;

    // For updates, fields are optional but must be valid if provided
    if (title !== undefined) {
      if (typeof title !== 'string' || title.trim().length === 0) {
        ResponseUtil.badRequest(res, 'Title must be a non-empty string');
        return;
      }
    }

    if (shortDescription !== undefined) {
      if (typeof shortDescription !== 'string' || shortDescription.trim().length === 0) {
        ResponseUtil.badRequest(res, 'Short description must be a non-empty string');
        return;
      }
    }

    if (description !== undefined) {
      if (typeof description !== 'string' || description.trim().length === 0) {
        ResponseUtil.badRequest(res, 'Description must be a non-empty string');
        return;
      }
    }

    if (variations !== undefined) {
      if (!Array.isArray(variations)) {
        ResponseUtil.badRequest(res, 'Variations must be an array');
        return;
      }

      for (const variation of variations) {
        if (!validateVariation(variation)) {
          ResponseUtil.badRequest(res, 'Invalid variation format');
          return;
        }
      }
    }

    if (quantity !== undefined) {
      if (!Array.isArray(quantity)) {
        ResponseUtil.badRequest(res, 'Quantity must be an array');
        return;
      }

      for (const qty of quantity) {
        if (!validateQuantity(qty)) {
          ResponseUtil.badRequest(res, 'Invalid quantity format');
          return;
        }
      }
    }

    next();
  } catch (error) {
    ResponseUtil.badRequest(res, 'Validation error', error instanceof Error ? error.message : 'Unknown error');
  }
};
