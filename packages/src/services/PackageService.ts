import { Package, IPackage } from '../models/Package';
import { PackageInterface } from '../types/package.interface';
import { logger } from '../utils/logger';

export class PackageService {
  async createPackage(packageData: PackageInterface): Promise<IPackage> {
    try {
      const newPackage = new Package(packageData);
      const savedPackage = await newPackage.save();
      logger.info(`Package created successfully with ID: ${savedPackage._id}`);
      return savedPackage;
    } catch (error) {
      logger.error('Error creating package:', error);
      throw error;
    }
  }

  async getAllPackages(
    page: number = 1,
    limit: number = 10,
    sortBy: string = 'createdAt',
    sortOrder: 'asc' | 'desc' = 'desc'
  ): Promise<{
    packages: IPackage[];
    total: number;
    page: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    try {
      const skip = (page - 1) * limit;
      const sortOptions: any = {};
      sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

      const [packages, total] = await Promise.all([
        Package.find()
          .sort(sortOptions)
          .skip(skip)
          .limit(limit)
          .lean(),
        Package.countDocuments(),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        packages: packages as IPackage[],
        total,
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      logger.error('Error fetching packages:', error);
      throw error;
    }
  }

  async getPackageById(id: string): Promise<IPackage | null> {
    try {
      const package_ = await Package.findById(id).lean();
      return package_ as IPackage | null;
    } catch (error) {
      logger.error(`Error fetching package with ID ${id}:`, error);
      throw error;
    }
  }

  async updatePackage(id: string, updateData: Partial<PackageInterface>): Promise<IPackage | null> {
    try {
      const updatedPackage = await Package.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).lean();

      if (updatedPackage) {
        logger.info(`Package updated successfully with ID: ${id}`);
      }

      return updatedPackage as IPackage | null;
    } catch (error) {
      logger.error(`Error updating package with ID ${id}:`, error);
      throw error;
    }
  }

  async deletePackage(id: string): Promise<boolean> {
    try {
      const deletedPackage = await Package.findByIdAndDelete(id);
      
      if (deletedPackage) {
        logger.info(`Package deleted successfully with ID: ${id}`);
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error(`Error deleting package with ID ${id}:`, error);
      throw error;
    }
  }

  async searchPackages(
    searchTerm: string,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    packages: IPackage[];
    total: number;
    page: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    try {
      const skip = (page - 1) * limit;
      
      const searchQuery = {
        $or: [
          { title: { $regex: searchTerm, $options: 'i' } },
          { shortDescription: { $regex: searchTerm, $options: 'i' } },
          { description: { $regex: searchTerm, $options: 'i' } },
          { 'variations.title': { $regex: searchTerm, $options: 'i' } },
        ],
      };

      const [packages, total] = await Promise.all([
        Package.find(searchQuery)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Package.countDocuments(searchQuery),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        packages: packages as IPackage[],
        total,
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      logger.error('Error searching packages:', error);
      throw error;
    }
  }

  async getPackagesByPriceRange(
    minPrice: number,
    maxPrice: number,
    page: number = 1,
    limit: number = 10
  ): Promise<{
    packages: IPackage[];
    total: number;
    page: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    try {
      const skip = (page - 1) * limit;
      
      const priceQuery = {
        'quantity.price': {
          $gte: minPrice,
          $lte: maxPrice,
        },
      };

      const [packages, total] = await Promise.all([
        Package.find(priceQuery)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        Package.countDocuments(priceQuery),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        packages: packages as IPackage[],
        total,
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error) {
      logger.error('Error fetching packages by price range:', error);
      throw error;
    }
  }
}
