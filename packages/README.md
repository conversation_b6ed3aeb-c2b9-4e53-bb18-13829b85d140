# Packages Microservice

A Node.js microservice for managing packages with variations and quantities, built with Express.js, TypeScript, and MongoDB.

## Features

- **Package Management**: Create, read, update, and delete packages
- **Variations Support**: Handle different package variations (images, default, dropdown)
- **Quantity Options**: Manage different sizes and pricing
- **Search Functionality**: Search packages by title, description, and variations
- **Price Range Filtering**: Filter packages by price range
- **Pagination**: Efficient pagination for large datasets
- **Authentication**: JWT-based authentication for admin operations
- **Validation**: Comprehensive input validation
- **Error Handling**: Robust error handling and logging
- **TypeScript**: Full TypeScript support for type safety

## API Endpoints

### Public Endpoints

- `GET /api/packages` - Get all packages with pagination
- `GET /api/packages/:id` - Get package by ID
- `GET /api/packages/search?q=term` - Search packages
- `GET /api/packages/price-range?minPrice=0&maxPrice=100` - Get packages by price range
- `GET /api/health` - Service health check
- `GET /api/` - API documentation

### Admin Endpoints (Authentication Required)

- `POST /api/packages` - Create new package
- `PUT /api/packages/:id` - Update package
- `PATCH /api/packages/:id` - Partially update package
- `DELETE /api/packages/:id` - Delete package

## Data Structure

### Package Interface

```typescript
interface PackageInterface {
  title: string;
  shortDescription: string;
  description: string;
  variations: Variations[];
  quantity: Quantity[];
}

interface Variations {
  title: string;
  optionType: "images" | "default" | "dropdown";
  options: string[];
}

interface Quantity {
  size: string;
  price: number;
}
```

## Installation

1. Navigate to the packages directory:
```bash
cd packages
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Build the project:
```bash
npm run build
```

## Development

Start the development server:
```bash
npm run dev
```

The service will be available at `http://localhost:8001`

## Production

Build and start the production server:
```bash
npm run build
npm start
```

## Database Seeding

To populate the database with sample data:
```bash
npm run seed
```

## Environment Variables

- `PORT` - Server port (default: 8001)
- `MONGO_URI` - MongoDB connection string
- `JWT_SECRET` - JWT secret for user authentication
- `ADMIN_JWT_SECRET` - JWT secret for admin authentication
- `NODE_ENV` - Environment (development/production)

## Authentication

### User Authentication
Include the JWT token in the Authorization header:
```
Authorization: Bearer <user_jwt_token>
```

### Admin Authentication
Include the admin JWT token in the Authorization header:
```
Authorization: Bearer <admin_jwt_token>
```

## API Examples

### Create Package (Admin)
```bash
curl -X POST http://localhost:8001/api/packages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "title": "Premium Gift Box",
    "shortDescription": "Elegant gift packaging",
    "description": "Perfect for special occasions",
    "variations": [
      {
        "title": "Color",
        "optionType": "dropdown",
        "options": ["Red", "Blue", "Gold"]
      }
    ],
    "quantity": [
      {
        "size": "Small",
        "price": 15.99
      }
    ]
  }'
```

### Get All Packages
```bash
curl http://localhost:8001/api/packages?page=1&limit=10
```

### Search Packages
```bash
curl "http://localhost:8001/api/packages/search?q=gift&page=1&limit=10"
```

### Get Packages by Price Range
```bash
curl "http://localhost:8001/api/packages/price-range?minPrice=10&maxPrice=50"
```

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Logging

The service includes comprehensive logging for:
- Request/response cycles
- Database operations
- Error tracking
- Performance monitoring

## Testing

Run tests:
```bash
npm test
```

## Contributing

1. Follow the existing code structure
2. Add appropriate validation
3. Include error handling
4. Update documentation
5. Add tests for new features

## License

ISC
