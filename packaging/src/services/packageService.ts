import Package from "../models/Package";

export class PackageService {
  // Get all packages with pagination
  async getAllPackages(page: number = 1, limit: number = 10) {
    try {
      const skip = (page - 1) * limit;
      const packages = await Package.find().skip(skip).limit(limit);
      const total = await Package.countDocuments();

      return { packages, total };
    } catch (error) {
      throw new Error('Failed to retrieve packages');
    }
  }

  // Get package by ID
  async getPackageById(id: string) {
    try {
      const package_ = await Package.findById(id);
      return package_;
    } catch (error) {
      throw new Error('Failed to retrieve package');
    }
  }

  // Create new package
  async createPackage(packageData: any) {
    try {
      // Check if package with same title already exists
      const existingPackage = await Package.findOne({ title: packageData.title });
      if (existingPackage) {
        throw new Error('Package with this title already exists');
      }

      const newPackage = await Package.create(packageData);
      return newPackage;
    } catch (error) {
      throw error;
    }
  }

  // Update package
  async updatePackage(id: string, updateData: any) {
    try {
      const updatedPackage = await Package.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );
      return updatedPackage;
    } catch (error) {
      throw new Error('Failed to update package');
    }
  }

  // Delete package
  async deletePackage(id: string) {
    try {
      const deletedPackage = await Package.findByIdAndDelete(id);
      return deletedPackage;
    } catch (error) {
      throw new Error('Failed to delete package');
    }
  }

  // Search packages
  async searchPackages(query: string, page: number = 1, limit: number = 10) {
    try {
      const skip = (page - 1) * limit;
      const searchRegex = new RegExp(query, 'i');

      const packages = await Package.find({
        $or: [
          { title: searchRegex },
          { shortDescription: searchRegex },
          { description: searchRegex }
        ]
      }).skip(skip).limit(limit);

      const total = await Package.countDocuments({
        $or: [
          { title: searchRegex },
          { shortDescription: searchRegex },
          { description: searchRegex }
        ]
      });

      return { packages, total };
    } catch (error) {
      throw new Error('Failed to search packages');
    }
  }
}

export default new PackageService();
