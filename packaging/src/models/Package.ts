import mongoose from "mongoose";

const VariationSchema = new mongoose.Schema({
  title: { type: String, required: true },
  optionType: { type: String, enum: ["images", "default", "dropdown"], required: true },
  options: [{ type: String, required: true }],
});

const QuantitySchema = new mongoose.Schema({
  size: { type: String, required: true },
  price: { type: Number, required: true },
});

const PackageSchema = new mongoose.Schema({
  title: { type: String, required: true },
  shortDescription: { type: String },
  description: { type: String },
  variations: [VariationSchema],
  quantity: [QuantitySchema],
});

export default mongoose.model("Package", PackageSchema);
