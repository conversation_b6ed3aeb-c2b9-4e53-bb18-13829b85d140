import { Request, Response, NextFunction } from "express";
import Package from "../models/Package";
import { sendErrorResponse } from "../utils/sendResponse";

declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

interface AuthRequest extends Request {
  user?: any;
}

export class PackageController {
  // Get all packages
  async getAllPackages(req: Request, res: Response, next: NextFunction) {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      const packages = await Package.find().skip(skip).limit(limit);
      const total = await Package.countDocuments();

      res.status(200).json({
        success: true,
        packages,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          limit,
          totalItems: total,
        },
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  // Get package by ID
  async getPackageById(req: Request, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const package_ = await Package.findById(id);

      if (!package_) {
        return res.status(404).json({
          success: false,
          message: "Package not found",
        });
      }

      res.status(200).json({
        success: true,
        package: package_,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  // Create new package
  async createPackage(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const packageData = req.body;
      const newPackage = await Package.create(packageData);

      res.status(201).json({
        success: true,
        message: "Package created successfully",
        package: newPackage,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  // Update package
  async updatePackage(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const updatedPackage = await Package.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      );

      if (!updatedPackage) {
        return res.status(404).json({
          success: false,
          message: "Package not found",
        });
      }

      res.status(200).json({
        success: true,
        message: "Package updated successfully",
        package: updatedPackage,
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  // Delete package
  async deletePackage(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      const { id } = req.params;
      const deletedPackage = await Package.findByIdAndDelete(id);

      if (!deletedPackage) {
        return res.status(404).json({
          success: false,
          message: "Package not found",
        });
      }

      res.status(200).json({
        success: true,
        message: "Package deleted successfully",
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }

  // Search packages
  async searchPackages(req: Request, res: Response, next: NextFunction) {
    try {
      const query = req.query.q as string;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const skip = (page - 1) * limit;

      if (!query) {
        return res.status(400).json({
          success: false,
          message: "Search query is required",
        });
      }

      const searchRegex = new RegExp(query, 'i');
      const packages = await Package.find({
        $or: [
          { title: searchRegex },
          { shortDescription: searchRegex },
          { description: searchRegex }
        ]
      }).skip(skip).limit(limit);

      const total = await Package.countDocuments({
        $or: [
          { title: searchRegex },
          { shortDescription: searchRegex },
          { description: searchRegex }
        ]
      });

      res.status(200).json({
        success: true,
        packages,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          limit,
          totalItems: total,
        },
      });
    } catch (error) {
      console.log(error);
      next(error);
    }
  }
}

const packageController = new PackageController();
export default packageController;
